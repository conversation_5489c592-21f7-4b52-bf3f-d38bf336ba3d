{% comment %}
  ============================================================================
  Shopify Section: Promo Bar with Countdown and Purchase Notifications
  - Updated to follow Shopify Liquid best practices and official documentation
  - Improved accessibility, performance, and code organization
  ============================================================================
{% endcomment %}

{% comment %} Main promo bar container {% endcomment %}
<div id="promo-bar-container-{{ section.id }}" class="promo-bar-wrapper">
  <div class="cs-flash-sale-bar" style="background-color: {{ section.settings.bar_bg_color }};" role="banner" aria-label="Promotional banner">
    <p>{{ section.settings.promo_text | escape }}</p>

    {% comment %} Countdown timer display {% endcomment %}
    <div class="cs-countdown" role="timer" aria-label="Sale countdown timer">
      <div class="cs-countdown-item">
        <span class="cs-countdown-number" data-unit="days" aria-label="Days remaining">00</span>
        <span class="cs-countdown-label">{{ section.settings.label_days | escape }}</span>
      </div>
      <div class="cs-countdown-item">
        <span class="cs-countdown-number" data-unit="hours" aria-label="Hours remaining">00</span>
        <span class="cs-countdown-label">{{ section.settings.label_hours | escape }}</span>
      </div>
      <div class="cs-countdown-item">
        <span class="cs-countdown-number" data-unit="minutes" aria-label="Minutes remaining">00</span>
        <span class="cs-countdown-label">{{ section.settings.label_mins | escape }}</span>
      </div>
      <div class="cs-countdown-item">
        <span class="cs-countdown-number" data-unit="seconds" aria-label="Seconds remaining">00</span>
        <span class="cs-countdown-label">{{ section.settings.label_secs | escape }}</span>
      </div>
    </div>

    {% comment %} Call-to-action button {% endcomment %}
    {% if section.settings.cta_link != blank and section.settings.cta_text != blank %}
      <a href="{{ section.settings.cta_link }}" class="cs-cta-button" aria-label="{{ section.settings.cta_text | escape }}">
        {{ section.settings.cta_text | escape }}
      </a>
    {% endif %}
  </div>

  {% comment %} Purchase notification popup {% endcomment %}
  {% if section.settings.enable_popups %}
    <div class="cs-purchase-notification"
         id="purchase-notification-{{ section.id }}"
         role="alert"
         aria-live="polite"
         aria-label="Recent purchase notification">
      <img id="purchase-image-{{ section.id }}"
           src=""
           alt="Product image"
           class="cs-purchase-image"
           loading="lazy">
      <div class="cs-purchase-details" id="purchase-details-{{ section.id }}">
        {% comment %} Content is generated by JavaScript {% endcomment %}
      </div>
    </div>
  {% endif %}
</div>

{% comment %} Section-specific styles {% endcomment %}
<style>
  /* Main promo bar styles */
  #promo-bar-container-{{ section.id }} .cs-flash-sale-bar {
      background-color: {{ section.settings.bar_bg_color }};
      color: white;
      padding: 12px 20px;
      font-weight: 500;
      font-size: 16px;
      text-align: center;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 15px;
      box-sizing: border-box;
      z-index: 999;
  }

  /* Promo bar text styles */
  #promo-bar-container-{{ section.id }} .cs-flash-sale-bar p {
    margin: 0;
    padding: 0;
  }

  /* Countdown timer styles */
  #promo-bar-container-{{ section.id }} .cs-countdown {
    display: flex;
    gap: 10px;
    font-weight: 700;
  }
  #promo-bar-container-{{ section.id }} .cs-countdown-item {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    padding: 5px 10px;
    border-radius: 4px;
  }
  #promo-bar-container-{{ section.id }} .cs-countdown-number {
    font-size: 1.1em;
  }
  #promo-bar-container-{{ section.id }} .cs-countdown-label {
    font-size: 0.8em;
    opacity: 0.9;
  }

  /* Call-to-action button styles */
  #promo-bar-container-{{ section.id }} .cs-cta-button {
    background-color: #ffffff;
    color: {{ section.settings.bar_bg_color }};
    padding: 8px 18px;
    text-decoration: none;
    font-weight: bold;
    border-radius: 5px;
    transition: transform 0.2s ease, background-color 0.2s ease;
  }
  #promo-bar-container-{{ section.id }} .cs-cta-button:hover {
    background-color: #f2f2f2;
    transform: scale(1.05);
  }


  /* Purchase notification popup styles */
  #promo-bar-container-{{ section.id }} .cs-purchase-notification {
      position: fixed;
      {% comment %} Position notification based on settings {% endcomment %}
      {% case section.settings.notification_position %}
        {% when 'bottom-left' %}
          bottom: 20px;
          left: 20px;
        {% when 'bottom-right' %}
          bottom: 20px;
          right: 20px;
        {% when 'top-left' %}
          top: 80px;
          left: 20px;
        {% when 'top-right' %}
          top: 80px;
          right: 20px;
      {% endcase %}
      background-color: #fff;
      color: #333;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      gap: 15px;
      z-index: 1000;
      font-size: 14px;
      transform: {% if section.settings.notification_position contains 'top' %}translateY(-200%){% else %}translateY(200%){% endif %};
      opacity: 0;
      transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
      visibility: hidden;
      max-width: 320px;
  }
  #promo-bar-container-{{ section.id }} .cs-purchase-notification.show { transform: translateY(0); opacity: 1; visibility: visible; }
  #promo-bar-container-{{ section.id }} .cs-purchase-notification.fading-out { opacity: 0; transition: opacity 0.5s ease; }
  #promo-bar-container-{{ section.id }} .cs-purchase-image { width: 60px; height: 60px; border-radius: 6px; object-fit: cover; flex-shrink: 0; }
  #promo-bar-container-{{ section.id }} .cs-purchase-details a { font-weight: 600; color: {{ section.settings.product_link_color }}; text-decoration: none; }
  #promo-bar-container-{{ section.id }} .cs-purchase-details a:hover { text-decoration: underline; }
  #promo-bar-container-{{ section.id }} .cs-purchase-details small { display: block; color: #777; margin-top: 4px; font-size: 12px; }
  #promo-bar-container-{{ section.id }} .cs-purchase-line1 { color: #555; }
  #promo-bar-container-{{ section.id }} .cs-purchase-line2 { margin: 2px 0; }
  #promo-bar-container-{{ section.id }} .cs-purchase-price { font-weight: 600; color: #27ae60; margin: 4px 0; }

  @media (max-width: 768px) {
      #promo-bar-container-{{ section.id }} .cs-flash-sale-bar { flex-direction: column; gap: 10px; padding: 15px; }
      #promo-bar-container-{{ section.id }} .cs-purchase-notification { 
        {% case section.settings.notification_position %}
          {% when 'bottom-left' or 'bottom-right' %}
            left: 10px; 
            right: 10px; 
            bottom: 10px;
            top: auto;
          {% when 'top-left' or 'top-right' %}
            left: 10px; 
            right: 10px; 
            top: 70px;
            bottom: auto;
        {% endcase %}
        max-width: calc(100% - 20px); 
      }
  }
</style>

{% comment %} JavaScript functionality for promo bar {% endcomment %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const sectionId = '{{ section.id }}';
  const containerId = 'promo-bar-container-' + sectionId;

  {% comment %} Prevent the script from running multiple times for the same section {% endcomment %}
  if (window['promoBar' + sectionId]) return;
  window['promoBar' + sectionId] = true;

  function initializePromoBar() {
    {% comment %} Initialize countdown timer functionality {% endcomment %}
    const countdownContainer = document.querySelector('#' + containerId + ' .cs-countdown');
    if (countdownContainer) {
      const daysEl = countdownContainer.querySelector('[data-unit="days"]');
      const hoursEl = countdownContainer.querySelector('[data-unit="hours"]');
      const minutesEl = countdownContainer.querySelector('[data-unit="minutes"]');
      const secondsEl = countdownContainer.querySelector('[data-unit="seconds"]');

      const updateCountdown = function() {
          {% comment %} Calculate time remaining until midnight tomorrow {% endcomment %}
          const now = new Date();
          const tomorrow = new Date(now);
          tomorrow.setDate(now.getDate() + 1);
          tomorrow.setHours(0, 0, 0, 0);
          const distance = tomorrow - now;

          {% comment %} Calculate time units {% endcomment %}
          const days = Math.floor(distance / (1000 * 60 * 60 * 24));
          const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((distance % (1000 * 60)) / 1000);

          {% comment %} Update display with zero-padded values {% endcomment %}
          if(daysEl) daysEl.textContent = String(days).padStart(2, '0');
          if(hoursEl) hoursEl.textContent = String(hours).padStart(2, '0');
          if(minutesEl) minutesEl.textContent = String(minutes).padStart(2, '0');
          if(secondsEl) secondsEl.textContent = String(seconds).padStart(2, '0');
      };

      {% comment %} Start countdown and update every second {% endcomment %}
      updateCountdown();
      setInterval(updateCountdown, 1000);
    }

    {% comment %} Initialize purchase notification functionality {% endcomment %}
    const enablePopups = {{ section.settings.enable_popups | default: true | json }};
    if (!enablePopups) return;
    
    // Get products based on settings
    {% comment %} Select products from collection or all products {% endcomment %}
    {% if section.settings.products_collection %}
      {% assign products_temp = section.settings.products_collection.products %}
    {% else %}
      {% assign products_temp = collections.all.products %}
    {% endif %}

    {% comment %} Filter by availability if enabled {% endcomment %}
    {% if section.settings.only_available %}
      {% assign products_source = products_temp | where: 'available' %}
    {% else %}
      {% assign products_source = products_temp %}
    {% endif %}
    
    {% comment %} Limit products and convert to JSON for JavaScript {% endcomment %}
    {% assign products_pool_size = section.settings.products_pool_size | default: 50 %}
    {% assign limited_products = products_source | limit: products_pool_size %}
    const allProducts = {{ limited_products | json }};
    const onlyWithImages = {{ section.settings.only_with_images | default: false | json }};
    
    // Filter products with images if needed
    let filteredProducts = allProducts;
    if (onlyWithImages) {
        filteredProducts = allProducts.filter(function(product) {
            return product.featured_image || (product.images && product.images.length > 0);
        });
    }
    
    // Randomly select products from the larger pool
    function getRandomProducts(sourceArray, count) {
        const shuffled = sourceArray.slice();
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled.slice(0, count);
    }
    
    const products = filteredProducts.length > 0 ? getRandomProducts(filteredProducts, Math.min(10, filteredProducts.length)) : [];

    function parseCustomerData(dataString) {
        if (!dataString) return [];
        try {
            return dataString.split('\n').map(function(line) {
                const parts = line.split(',');
                if (parts.length < 2) return null;
                return { name: parts[0].trim(), city: parts.slice(1).join(',').trim() };
            }).filter(function(item) { return item !== null; });
        } catch (e) {
            console.error('Error parsing customer data:', e);
            return [];
        }
    }

    {% comment %} Customer data for different regions {% endcomment %}
    const customerData = {
        usa: parseCustomerData({{ section.settings.customers_usa | json }}),
        eu: parseCustomerData({{ section.settings.customers_eu | json }}),
        japan: parseCustomerData({{ section.settings.customers_japan | json }})
    };

    const notificationEl = document.getElementById('purchase-notification-' + sectionId);
    const imageEl = document.getElementById('purchase-image-' + sectionId);
    const detailsEl = document.getElementById('purchase-details-' + sectionId);
    
    if (!notificationEl || !imageEl || !detailsEl) {
        console.error("Promo Bar: Notification elements not found.");
        return;
    }

    {% comment %} Timing settings for notifications {% endcomment %}
    const popupDuration = {{ section.settings.popup_duration | default: 7 | times: 1000 }};
    const firstPopupDelay = {{ section.settings.first_popup_delay | default: 4 | times: 1000 }};
    const minInterval = {{ section.settings.min_interval | default: 8 | times: 1000 }};
    const maxInterval = {{ section.settings.max_interval | default: 18 | times: 1000 }};

    function showRandomPurchase() {
        try {
            if (!products || products.length === 0) return;

            {% comment %} Regional probability settings {% endcomment %}
            const usaProbability = {{ section.settings.usa_probability | default: 70 | divided_by: 100.0 }};
            const euProbability = {{ section.settings.eu_probability | default: 20 | divided_by: 100.0 }};
            
            const rand = Math.random();
            let regionKey;
            if (rand < usaProbability) regionKey = 'usa';
            else if (rand < (usaProbability + euProbability)) regionKey = 'eu';
            else regionKey = 'japan';

            const regionalData = customerData[regionKey];
            if (!regionalData || regionalData.length === 0) return;

            const customer = regionalData[Math.floor(Math.random() * regionalData.length)];
            const product = products[Math.floor(Math.random() * products.length)];
            
            const productUrl = '/products/' + product.handle;
            const imageUrl = product.featured_image || product.images?.[0] || '';
            const showPrice = {{ section.settings.show_product_price | default: false | json }};

            if (imageUrl) {
                imageEl.src = imageUrl;
                imageEl.style.display = 'block';
            } else {
                imageEl.style.display = 'none';
            }

            {% comment %} Calculate random time ago for notification {% endcomment %}
            const maxHoursAgo = {{ section.settings.max_hours_ago | default: 5 }};
            const totalSecondsAgo = Math.floor(Math.random() * (maxHoursAgo * 3600));
            let timeAgoString = 'a few seconds ago';
            if (totalSecondsAgo >= 60 && totalSecondsAgo < 3600) {
                const minutes = Math.floor(totalSecondsAgo / 60);
                timeAgoString = minutes + ' minute' + (minutes === 1 ? '' : 's') + ' ago';
            } else if (totalSecondsAgo >= 3600) {
                const hours = Math.floor(totalSecondsAgo / 3600);
                timeAgoString = 'about ' + hours + ' hour' + (hours === 1 ? '' : 's') + ' ago';
            }

            let priceHtml = '';
            if (showPrice && product.price) {
                const price = (product.price / 100).toFixed(2);
                const currency = {{ section.settings.currency_symbol | default: '$' | json }};
                priceHtml = '<div class="cs-purchase-price">' + currency + ' ' + price + '</div>';
            }

            const notificationText = {{ section.settings.notification_text | default: 'just purchased:' | json }};

            detailsEl.innerHTML =
                '<div class="cs-purchase-line1">' + customer.name + ' in ' + customer.city + ' ' + notificationText + '</div>' +
                '<div class="cs-purchase-line2"><a href="' + productUrl + '" target="_blank">' + product.title + '</a></div>' +
                priceHtml +
                '<small>' + timeAgoString + '</small>';

            notificationEl.classList.remove('fading-out');
            notificationEl.classList.add('show');

            setTimeout(function() {
                notificationEl.classList.add('fading-out');
                setTimeout(function() {
                    notificationEl.classList.remove('show', 'fading-out');
                }, 500);
            }, popupDuration);
        } catch (e) {
            console.error('Error showing purchase notification:', e);
        }
    }

    function startNotifier() {
        if (!products || products.length === 0) {
          console.log("Promo Bar: No products found to display in popups. Check your filters (available products, products with images).");
          return;
        }
        console.log("Promo Bar: Starting notifications with " + products.length + " products");
        setTimeout(showRandomPurchase, firstPopupDelay);
        setInterval(function() {
            showRandomPurchase();
        }, Math.random() * (maxInterval - minInterval) + minInterval);
    }
    
    startNotifier();
  }

  initializePromoBar();
});
</script>

{% comment %} Section schema configuration {% endcomment %}
{% schema %}
{
  "name": "Promo Bar with Popups",
  "tag": "section",
  "class": "promo-bar-section",
  "settings": [
    {
      "type": "header",
      "content": "Promo Bar Settings"
    },
    {
      "type": "text",
      "id": "promo_text",
      "label": "Bar text",
      "default": "Flash Sale! 50% Off Storewide! Ends in:"
    },
    {
      "type": "color",
      "id": "bar_bg_color",
      "label": "Bar background color",
      "default": "#551BDA"
    },
    {
      "type": "text",
      "id": "cta_text",
      "label": "Button text",
      "default": "Shop Now"
    },
    {
      "type": "url",
      "id": "cta_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Countdown Labels"
    },
    {
      "type": "text",
      "id": "label_days",
      "label": "Days text",
      "default": "Days"
    },
    {
      "type": "text",
      "id": "label_hours",
      "label": "Hours text",
      "default": "Hrs"
    },
    {
      "type": "text",
      "id": "label_mins",
      "label": "Minutes text",
      "default": "Mins"
    },
    {
      "type": "text",
      "id": "label_secs",
      "label": "Seconds text",
      "default": "Secs"
    },
    {
      "type": "header",
      "content": "Purchase Notification Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_popups",
      "label": "Enable purchase popups",
      "default": true
    },
    {
      "type": "collection",
      "id": "products_collection",
      "label": "Products collection",
      "info": "Select collection to show products from. Leave empty for all products."
    },
    {
      "type": "range",
      "id": "products_pool_size",
      "min": 10,
      "max": 200,
      "step": 10,
      "unit": "pcs",
      "label": "Number of products to load",
      "default": 50,
      "info": "More products = more variety, but slower loading"
    },
    {
      "type": "checkbox",
      "id": "only_with_images",
      "label": "Show only products with images",
      "default": false,
      "info": "Hide products without featured images from notifications"
    },
    {
      "type": "checkbox",
      "id": "only_available",
      "label": "Show only available products",
      "default": true,
      "info": "Hide out-of-stock products from notifications"
    },
    {
      "type": "checkbox",
      "id": "show_product_price",
      "label": "Show product price in notifications",
      "default": false,
      "info": "Display the product price in purchase notifications"
    },
    {
      "type": "text",
      "id": "currency_symbol",
      "label": "Currency symbol",
      "default": "$",
      "info": "Currency symbol to display with prices"
    },
    {
      "type": "text",
      "id": "notification_text",
      "label": "Notification text",
      "default": "just purchased:",
      "info": "Text to display in purchase notifications (e.g., 'just purchased:', 'bought:', 'ordered:')"
    },
    {
      "type": "range",
      "id": "popup_duration",
      "min": 1,
      "max": 15,
      "step": 1,
      "unit": "s",
      "label": "Popup display duration",
      "default": 7
    },
    {
      "type": "color",
      "id": "product_link_color",
      "label": "Product link color",
      "default": "#551BDA"
    },
    {
      "type": "select",
      "id": "notification_position",
      "label": "Notification position",
      "default": "bottom-left",
      "options": [
        {
          "value": "bottom-left",
          "label": "Bottom Left"
        },
        {
          "value": "bottom-right",
          "label": "Bottom Right"
        },
        {
          "value": "top-left",
          "label": "Top Left"
        },
        {
          "value": "top-right",
          "label": "Top Right"
        }
      ]
    },
    {
      "type": "range",
      "id": "max_hours_ago",
      "min": 1,
      "max": 24,
      "step": 1,
      "unit": "h",
      "label": "Maximum 'time ago' to show",
      "default": 5,
      "info": "Notifications will show purchases from 0 to X hours ago"
    },
    {
      "type": "range",
      "id": "first_popup_delay",
      "min": 1,
      "max": 20,
      "step": 1,
      "unit": "s",
      "label": "Delay before first popup",
      "default": 4,
      "info": "How long to wait after page load"
    },
    {
      "type": "range",
      "id": "min_interval",
      "min": 2,
      "max": 30,
      "step": 1,
      "unit": "s",
      "label": "Minimum interval",
      "default": 8
    },
    {
      "type": "range",
      "id": "max_interval",
      "min": 3,
      "max": 60,
      "step": 1,
      "unit": "s",
      "label": "Maximum interval",
      "default": 18
    },
    {
      "type": "header",
      "content": "Customer Data & Regional Settings"
    },
    {
      "type": "paragraph",
      "content": "Configure customer data and regional probability settings for purchase notifications"
    },
    {
      "type": "range",
      "id": "usa_probability",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "USA customers probability",
      "default": 70
    },
    {
      "type": "range",
      "id": "eu_probability",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "EU customers probability",
      "default": 20,
      "info": "Japan gets the remaining percentage"
    },
    {
      "type": "textarea",
      "id": "customers_usa",
      "label": "USA Customers",
      "info": "Each line in format: Name, City, State. Ex: Michael, New York, NY",
      "default": "Michael, New York, NY\nJames, Los Angeles, CA\nDavid, Chicago, IL\nChris, Houston, TX\nJohn, Phoenix, AZ\nRobert, Philadelphia, PA\nDaniel, San Antonio, TX\nMark, San Diego, CA\nPaul, Dallas, TX\nKevin, Austin, TX\nSteven, Seattle, WA\nAndrew, Denver, CO\nBrian, Boston, MA\nGary, Miami, FL"
    },
    {
      "type": "textarea",
      "id": "customers_eu",
      "label": "EU Customers",
      "info": "Each line in format: Name, City, Country. Ex: Klaus, Berlin, Germany",
      "default": "Klaus, Berlin, Germany\nHans, Munich, Germany\nStefan, Hamburg, Germany\nPierre, Paris, France\nMichel, Marseille, France\nJulien, Lyon, France\nJavier, Madrid, Spain\nCarlos, Barcelona, Spain\nAlejandro, Valencia, Spain\nGiovanni, Rome, Italy\nMarco, Milan, Italy\nAlessandro, Naples, Italy\nLars, Amsterdam, Netherlands\nSven, Rotterdam, Netherlands\nJan, Prague, Czech Republic\nPetr, Brno, Czech Republic\nTomas, Ostrava, Czech Republic\nMateusz, Warsaw, Poland\nPiotr, Krakow, Poland\nKrzysztof, Gdansk, Poland\nJames, London, UK\nOliver, Manchester, UK\nHarry, Birmingham, UK\nErik, Stockholm, Sweden\nMikael, Gothenburg, Sweden\nAnders, Malmo, Sweden"
    },
    {
      "type": "textarea",
      "id": "customers_japan",
      "label": "Japan Customers",
      "info": "Each line in format: Name, City, Country. Ex: Taro, Tokyo, Japan",
      "default": "Taro, Tokyo, Japan\nKenji, Osaka, Japan\nSatoshi, Kyoto, Japan\nTakeshi, Yokohama, Japan\nHaruki, Nagoya, Japan\nMinato, Sapporo, Japan\nRen, Fukuoka, Japan\nYamato, Kobe, Japan"
    }
  ],
  "presets": [
    {
      "name": "Promo Bar with Popups"
    }
  ]
}